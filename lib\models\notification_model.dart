class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String recipientType;
  final List<int>? studentIds;
  final List<String>? studentClasses;
  final List<int>? employeeIds;
  final List<String>? employeeContractTypes;
  final bool hasAttachments;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final AdminModel? admin;
  final List<AttachmentModel> attachments;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.recipientType,
    this.studentIds,
    this.studentClasses,
    this.employeeIds,
    this.employeeContractTypes,
    required this.hasAttachments,
    required this.isRead,
    required this.createdAt,
    this.updatedAt,
    this.admin,
    this.attachments = const [],
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      recipientType: json['recipient_type'] ?? '',
      studentIds: json['student_ids'] != null 
          ? List<int>.from(json['student_ids']) 
          : null,
      studentClasses: json['student_classes'] != null 
          ? List<String>.from(json['student_classes']) 
          : null,
      employeeIds: json['employee_ids'] != null 
          ? List<int>.from(json['employee_ids']) 
          : null,
      employeeContractTypes: json['employee_contract_types'] != null 
          ? List<String>.from(json['employee_contract_types']) 
          : null,
      hasAttachments: json['has_attachments'] ?? false,
      isRead: json['is_read'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      admin: json['admin'] != null 
          ? AdminModel.fromJson(json['admin']) 
          : null,
      attachments: json['attachments'] != null
          ? (json['attachments'] as List)
              .map((attachment) => AttachmentModel.fromJson(attachment))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'recipient_type': recipientType,
      'student_ids': studentIds,
      'student_classes': studentClasses,
      'employee_ids': employeeIds,
      'employee_contract_types': employeeContractTypes,
      'has_attachments': hasAttachments,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'admin': admin?.toJson(),
      'attachments': attachments.map((attachment) => attachment.toJson()).toList(),
    };
  }

  NotificationModel copyWith({
    int? id,
    String? title,
    String? message,
    String? recipientType,
    List<int>? studentIds,
    List<String>? studentClasses,
    List<int>? employeeIds,
    List<String>? employeeContractTypes,
    bool? hasAttachments,
    bool? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
    AdminModel? admin,
    List<AttachmentModel>? attachments,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      recipientType: recipientType ?? this.recipientType,
      studentIds: studentIds ?? this.studentIds,
      studentClasses: studentClasses ?? this.studentClasses,
      employeeIds: employeeIds ?? this.employeeIds,
      employeeContractTypes: employeeContractTypes ?? this.employeeContractTypes,
      hasAttachments: hasAttachments ?? this.hasAttachments,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      admin: admin ?? this.admin,
      attachments: attachments ?? this.attachments,
    );
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  String get recipientTypeDisplay {
    switch (recipientType) {
      case 'students':
        return 'All Students';
      case 'employees':
        return 'All Employees';
      case 'both':
        return 'Students & Employees';
      case 'specific_students':
        return 'Specific Students';
      case 'specific_employees':
        return 'Specific Employees';
      case 'student_classes':
        return 'Student Classes';
      case 'employee_contract_types':
        return 'Employee Contract Types';
      default:
        return 'Unknown';
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class AdminModel {
  final int id;
  final String name;
  final String email;

  AdminModel({
    required this.id,
    required this.name,
    required this.email,
  });

  factory AdminModel.fromJson(Map<String, dynamic> json) {
    return AdminModel(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
    };
  }
}

class AttachmentModel {
  final int id;
  final String originalName;
  final String filePath;
  final String fileType;
  final String fileExtension;
  final int fileSize;
  final String fileSizeHuman;

  AttachmentModel({
    required this.id,
    required this.originalName,
    required this.filePath,
    required this.fileType,
    required this.fileExtension,
    required this.fileSize,
    required this.fileSizeHuman,
  });

  factory AttachmentModel.fromJson(Map<String, dynamic> json) {
    return AttachmentModel(
      id: json['id'] ?? 0,
      originalName: json['original_name'] ?? '',
      filePath: json['file_path'] ?? '',
      fileType: json['file_type'] ?? '',
      fileExtension: json['file_extension'] ?? '',
      fileSize: json['file_size'] ?? 0,
      fileSizeHuman: json['file_size_human'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'original_name': originalName,
      'file_path': filePath,
      'file_type': fileType,
      'file_extension': fileExtension,
      'file_size': fileSize,
      'file_size_human': fileSizeHuman,
    };
  }

  bool get isImage {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.contains(fileExtension.toLowerCase());
  }

  bool get isPdf => fileExtension.toLowerCase() == 'pdf';

  bool get isDocument {
    const docExtensions = ['doc', 'docx', 'txt'];
    return docExtensions.contains(fileExtension.toLowerCase());
  }

  String get iconName {
    if (isImage) return 'image';
    if (isPdf) return 'pdf';
    if (isDocument) return 'document';
    return 'file';
  }
}
