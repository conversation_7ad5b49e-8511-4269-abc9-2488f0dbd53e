<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="NotiSend - نظام إدارة الإشعارات">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="NotiSend">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>NotiSend - نظام إدارة الإشعارات</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #6b8e23 0%, #556b2f 100%);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
      text-align: center;
    }

    .logo {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      animation: pulse 2s infinite;
    }

    .logo::before {
      content: "🔔";
      font-size: 40px;
    }

    .app-title {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 8px;
      animation: fadeInUp 1s ease-out;
    }

    .app-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin-bottom: 48px;
      animation: fadeInUp 1s ease-out 0.2s both;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-top: 16px;
      font-size: 14px;
      opacity: 0.8;
      animation: fadeInUp 1s ease-out 0.4s both;
    }

    .version {
      position: absolute;
      bottom: 32px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
      opacity: 0.6;
      animation: fadeInUp 1s ease-out 0.6s both;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Hide loading when Flutter is ready */
    .flutter-ready .loading-container {
      display: none;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .app-title {
        font-size: 24px;
      }
      
      .app-subtitle {
        font-size: 14px;
      }
      
      .logo {
        width: 60px;
        height: 60px;
      }
      
      .logo::before {
        font-size: 30px;
      }
    }

    /* RTL Support */
    [dir="rtl"] .loading-container {
      direction: rtl;
    }
  </style>
</head>
<body>
  <div class="loading-container" id="loading">
    <div class="logo"></div>
    <div class="app-title">NotiSend</div>
    <div class="app-subtitle">نظام إدارة الإشعارات</div>
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري التحميل...</div>
    <div class="version">الإصدار 1.0.0</div>
  </div>

  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            // Hide loading screen
            document.getElementById('loading').style.display = 'none';
            document.body.classList.add('flutter-ready');
            
            // Run Flutter app
            appRunner.runApp();
          });
        }
      });
    });

    // Handle loading errors
    window.addEventListener('error', function(e) {
      console.error('Loading error:', e);
      document.querySelector('.loading-text').textContent = 'حدث خطأ في التحميل...';
      document.querySelector('.loading-spinner').style.display = 'none';
    });

    // Add some interactivity to loading screen
    document.addEventListener('DOMContentLoaded', function() {
      const logo = document.querySelector('.logo');
      logo.addEventListener('click', function() {
        logo.style.animation = 'pulse 0.5s ease-in-out';
        setTimeout(() => {
          logo.style.animation = 'pulse 2s infinite';
        }, 500);
      });
    });
  </script>
</body>
</html>
