import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;

  // Initialize SharedPreferences
  Future<void> init() async {
    try {
      _prefs ??= await SharedPreferences.getInstance();
    } catch (e) {
      debugPrint('SharedPreferences initialization failed: $e');
      // For web compatibility, we'll continue without storage
      rethrow;
    }
  }

  // Ensure preferences are initialized
  Future<SharedPreferences> get prefs async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // Token Management
  Future<void> saveToken(String token) async {
    final preferences = await prefs;
    await preferences.setString(AppConfig.userTokenKey, token);
  }

  Future<String?> getToken() async {
    final preferences = await prefs;
    return preferences.getString(AppConfig.userTokenKey);
  }

  Future<void> removeToken() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.userTokenKey);
  }

  // User Type Management
  Future<void> saveUserType(String userType) async {
    final preferences = await prefs;
    await preferences.setString(AppConfig.userTypeKey, userType);
  }

  Future<String?> getUserType() async {
    final preferences = await prefs;
    return preferences.getString(AppConfig.userTypeKey);
  }

  Future<void> removeUserType() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.userTypeKey);
  }

  // User Data Management
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    final preferences = await prefs;
    final userDataJson = jsonEncode(userData);
    await preferences.setString(AppConfig.userDataKey, userDataJson);
  }

  Future<Map<String, dynamic>?> getUserData() async {
    final preferences = await prefs;
    final userDataJson = preferences.getString(AppConfig.userDataKey);
    if (userDataJson != null) {
      return jsonDecode(userDataJson) as Map<String, dynamic>;
    }
    return null;
  }

  Future<void> removeUserData() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.userDataKey);
  }

  // Login Status Management
  Future<void> setLoggedIn(bool isLoggedIn) async {
    final preferences = await prefs;
    await preferences.setBool(AppConfig.isLoggedInKey, isLoggedIn);
  }

  Future<bool> isLoggedIn() async {
    final preferences = await prefs;
    return preferences.getBool(AppConfig.isLoggedInKey) ?? false;
  }

  Future<void> removeLoggedInStatus() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.isLoggedInKey);
  }

  // Last Sync Management
  Future<void> saveLastSync(DateTime lastSync) async {
    final preferences = await prefs;
    await preferences.setString(
        AppConfig.lastSyncKey, lastSync.toIso8601String());
  }

  Future<DateTime?> getLastSync() async {
    final preferences = await prefs;
    final lastSyncString = preferences.getString(AppConfig.lastSyncKey);
    if (lastSyncString != null) {
      return DateTime.parse(lastSyncString);
    }
    return null;
  }

  Future<void> removeLastSync() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.lastSyncKey);
  }

  // Notification Settings Management
  Future<void> saveNotificationSettings(Map<String, dynamic> settings) async {
    final preferences = await prefs;
    final settingsJson = jsonEncode(settings);
    await preferences.setString(
        AppConfig.notificationSettingsKey, settingsJson);
  }

  Future<Map<String, dynamic>?> getNotificationSettings() async {
    final preferences = await prefs;
    final settingsJson =
        preferences.getString(AppConfig.notificationSettingsKey);
    if (settingsJson != null) {
      return jsonDecode(settingsJson) as Map<String, dynamic>;
    }
    return null;
  }

  Future<void> removeNotificationSettings() async {
    final preferences = await prefs;
    await preferences.remove(AppConfig.notificationSettingsKey);
  }

  // Generic String Storage
  Future<void> saveString(String key, String value) async {
    final preferences = await prefs;
    await preferences.setString(key, value);
  }

  Future<String?> getString(String key) async {
    final preferences = await prefs;
    return preferences.getString(key);
  }

  Future<void> removeString(String key) async {
    final preferences = await prefs;
    await preferences.remove(key);
  }

  // Generic Boolean Storage
  Future<void> saveBool(String key, bool value) async {
    final preferences = await prefs;
    await preferences.setBool(key, value);
  }

  Future<bool?> getBool(String key) async {
    final preferences = await prefs;
    return preferences.getBool(key);
  }

  Future<void> removeBool(String key) async {
    final preferences = await prefs;
    await preferences.remove(key);
  }

  // Check if key exists
  Future<bool> containsKey(String key) async {
    final preferences = await prefs;
    return preferences.containsKey(key);
  }

  // Get all keys
  Future<Set<String>> getAllKeys() async {
    final preferences = await prefs;
    return preferences.getKeys();
  }

  // Clear specific keys
  Future<void> clearKeys(List<String> keys) async {
    final preferences = await prefs;
    for (String key in keys) {
      await preferences.remove(key);
    }
  }

  // Clear all data
  Future<void> clearAll() async {
    final preferences = await prefs;
    await preferences.clear();
  }

  // Clear user session data only
  Future<void> clearSession() async {
    await removeToken();
    await removeUserType();
    await removeUserData();
    await removeLoggedInStatus();
    await removeLastSync();
  }

  // Backup and restore functionality
  Future<Map<String, dynamic>> exportData() async {
    final preferences = await prefs;
    final keys = preferences.getKeys();
    Map<String, dynamic> data = {};

    for (String key in keys) {
      final value = preferences.get(key);
      data[key] = value;
    }

    return data;
  }

  Future<void> importData(Map<String, dynamic> data) async {
    final preferences = await prefs;

    for (String key in data.keys) {
      final value = data[key];

      if (value is String) {
        await preferences.setString(key, value);
      } else if (value is bool) {
        await preferences.setBool(key, value);
      } else if (value is int) {
        await preferences.setInt(key, value);
      } else if (value is double) {
        await preferences.setDouble(key, value);
      } else if (value is List<String>) {
        await preferences.setStringList(key, value);
      }
    }
  }

  // Get storage size (approximate)
  Future<int> getStorageSize() async {
    final data = await exportData();
    final jsonString = jsonEncode(data);
    return jsonString.length;
  }

  // Check if storage is healthy
  Future<bool> isHealthy() async {
    try {
      final preferences = await prefs;
      await preferences.setString('health_check', 'test');
      final value = preferences.getString('health_check');
      await preferences.remove('health_check');
      return value == 'test';
    } catch (e) {
      return false;
    }
  }
}
