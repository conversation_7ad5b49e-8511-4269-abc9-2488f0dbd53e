import 'package:flutter/material.dart';
import '../screens/splash_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/student/student_dashboard.dart';
import '../screens/student/student_notifications.dart';
import '../screens/employee/employee_dashboard.dart';
import '../screens/employee/employee_notifications.dart';
import '../screens/admin/admin_dashboard.dart';
import '../screens/admin/admin_notifications.dart';
import '../screens/shared/notification_detail_screen.dart';
import '../screens/shared/profile_screen.dart';
import '../screens/shared/settings_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';

  // Student routes
  static const String studentDashboard = '/student/dashboard';
  static const String studentNotifications = '/student/notifications';

  // Employee routes
  static const String employeeDashboard = '/employee/dashboard';
  static const String employeeNotifications = '/employee/notifications';

  // Admin routes
  static const String adminDashboard = '/admin/dashboard';
  static const String adminNotifications = '/admin/notifications';

  // Shared routes
  static const String notificationDetail = '/notification/detail';
  static const String profile = '/profile';
  static const String settings = '/settings';

  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: settings,
        );

      case login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
          settings: settings,
        );

      // Student routes
      case studentDashboard:
        return MaterialPageRoute(
          builder: (_) => const StudentDashboard(),
          settings: settings,
        );

      case studentNotifications:
        return MaterialPageRoute(
          builder: (_) => const StudentNotifications(),
          settings: settings,
        );

      // Employee routes
      case employeeDashboard:
        return MaterialPageRoute(
          builder: (_) => const EmployeeDashboard(),
          settings: settings,
        );

      case employeeNotifications:
        return MaterialPageRoute(
          builder: (_) => const EmployeeNotifications(),
          settings: settings,
        );

      // Admin routes
      case adminDashboard:
        return MaterialPageRoute(
          builder: (_) => const AdminDashboard(),
          settings: settings,
        );

      case adminNotifications:
        return MaterialPageRoute(
          builder: (_) => const AdminNotifications(),
          settings: settings,
        );

      // Shared routes
      case notificationDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final notificationId = args?['notificationId'] as int?;

        if (notificationId == null) {
          return _errorRoute('Notification ID is required');
        }

        return MaterialPageRoute(
          builder: (_) =>
              NotificationDetailScreen(notificationId: notificationId),
          settings: settings,
        );

      case profile:
        return MaterialPageRoute(
          builder: (_) => const ProfileScreen(),
          settings: settings,
        );

      case AppRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
          settings: settings,
        );

      default:
        return _errorRoute('Route not found: ${settings.name}');
    }
  }

  // Error route
  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Page Not Found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pushNamedAndRemoveUntil(
                  splash,
                  (route) => false,
                ),
                child: const Text('Go Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation helpers
  static void navigateToLogin(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      login,
      (route) => false,
    );
  }

  static void navigateToDashboard(BuildContext context, String userType) {
    String route;
    switch (userType) {
      case 'student':
        route = studentDashboard;
        break;
      case 'employee':
        route = employeeDashboard;
        break;
      case 'admin':
        route = adminDashboard;
        break;
      default:
        route = login;
    }

    Navigator.of(context).pushNamedAndRemoveUntil(
      route,
      (route) => false,
    );
  }

  static void navigateToNotifications(BuildContext context, String userType) {
    String route;
    switch (userType) {
      case 'student':
        route = studentNotifications;
        break;
      case 'employee':
        route = employeeNotifications;
        break;
      case 'admin':
        route = adminNotifications;
        break;
      default:
        return;
    }

    Navigator.of(context).pushNamed(route);
  }

  static void navigateToNotificationDetail(
      BuildContext context, int notificationId) {
    Navigator.of(context).pushNamed(
      notificationDetail,
      arguments: {'notificationId': notificationId},
    );
  }

  static void navigateToProfile(BuildContext context) {
    Navigator.of(context).pushNamed(profile);
  }

  static void navigateToSettings(BuildContext context) {
    Navigator.of(context).pushNamed(settings);
  }

  // Get initial route based on auth state
  static String getInitialRoute(bool isLoggedIn, String? userType) {
    if (!isLoggedIn) {
      return login;
    }

    switch (userType) {
      case 'student':
        return studentDashboard;
      case 'employee':
        return employeeDashboard;
      case 'admin':
        return adminDashboard;
      default:
        return login;
    }
  }

  // Check if route requires authentication
  static bool requiresAuth(String route) {
    const publicRoutes = [splash, login];
    return !publicRoutes.contains(route);
  }

  // Check if user has access to route
  static bool hasAccess(String route, String? userType) {
    if (userType == null) return false;

    // Admin has access to all routes
    if (userType == 'admin') return true;

    // Check specific route access
    if (route.startsWith('/student/') && userType != 'student') {
      return false;
    }

    if (route.startsWith('/employee/') && userType != 'employee') {
      return false;
    }

    if (route.startsWith('/admin/') && userType != 'admin') {
      return false;
    }

    return true;
  }

  // Get user-specific routes
  static List<String> getUserRoutes(String userType) {
    switch (userType) {
      case 'student':
        return [studentDashboard, studentNotifications];
      case 'employee':
        return [employeeDashboard, employeeNotifications];
      case 'admin':
        return [adminDashboard, adminNotifications];
      default:
        return [];
    }
  }

  // Get route title
  static String getRouteTitle(String route) {
    switch (route) {
      case splash:
        return 'NotiSend';
      case login:
        return 'Login';
      case studentDashboard:
        return 'Student Dashboard';
      case studentNotifications:
        return 'My Notifications';
      case employeeDashboard:
        return 'Employee Dashboard';
      case employeeNotifications:
        return 'My Notifications';
      case adminDashboard:
        return 'Admin Dashboard';
      case adminNotifications:
        return 'Manage Notifications';
      case notificationDetail:
        return 'Notification Details';
      case profile:
        return 'Profile';
      case settings:
        return 'Settings';
      default:
        return 'NotiSend';
    }
  }
}
