import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../services/api_service.dart';
import '../config/app_config.dart';

class NotificationProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  List<NotificationModel> _notifications = [];
  List<NotificationModel> _filteredNotifications = [];
  String _currentFilter = AppConfig.notificationTypeUnread;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  List<NotificationModel> get notifications => _filteredNotifications;
  List<NotificationModel> get allNotifications => _notifications;
  String get currentFilter => _currentFilter;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  bool get hasMoreData => _hasMoreData;

  // Statistics
  int get totalCount => _notifications.length;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  int get readCount => _notifications.where((n) => n.isRead).length;

  // Load notifications
  Future<void> loadNotifications(String userType,
      {bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _notifications.clear();
      _filteredNotifications.clear();
    }

    _setLoading(true);
    _clearError();

    try {
      // Get real notifications from API
      final notifications = await _apiService.getNotifications(
        userType,
        filter: _currentFilter,
        page: _currentPage,
        limit: AppConfig.defaultPageSize,
      );

      if (refresh) {
        _notifications = notifications;
      } else {
        _notifications.addAll(notifications);
      }

      _hasMoreData = notifications.length == AppConfig.defaultPageSize;
      _currentPage++;

      _applyFilter();
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Load more notifications (pagination)
  Future<void> loadMoreNotifications(String userType) async {
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final notifications = await _apiService.getNotifications(
        userType,
        filter: _currentFilter,
        page: _currentPage,
        limit: AppConfig.defaultPageSize,
      );

      _notifications.addAll(notifications);
      _hasMoreData = notifications.length == AppConfig.defaultPageSize;
      _currentPage++;

      _applyFilter();
    } catch (e) {
      _setError(e.toString());
    }

    _isLoadingMore = false;
    notifyListeners();
  }

  // Mark notification as read
  Future<bool> markAsRead(int notificationId, String userType) async {
    try {
      await _apiService.markNotificationAsRead(notificationId, userType);

      // Update local notification
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        _applyFilter();
      }

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead(String userType) async {
    final unreadNotifications = _notifications.where((n) => !n.isRead).toList();

    for (final notification in unreadNotifications) {
      await markAsRead(notification.id, userType);
    }
  }

  // Filter notifications
  void setFilter(String filter) {
    if (_currentFilter != filter) {
      _currentFilter = filter;
      _applyFilter();
    }
  }

  // Apply current filter
  void _applyFilter() {
    switch (_currentFilter) {
      case AppConfig.notificationTypeAll:
        _filteredNotifications = List.from(_notifications);
        break;
      case AppConfig.notificationTypeRead:
        _filteredNotifications = _notifications.where((n) => n.isRead).toList();
        break;
      case AppConfig.notificationTypeUnread:
        _filteredNotifications =
            _notifications.where((n) => !n.isRead).toList();
        break;
      default:
        _filteredNotifications = List.from(_notifications);
    }
    notifyListeners();
  }

  // Search notifications
  void searchNotifications(String query) {
    if (query.isEmpty) {
      _applyFilter();
      return;
    }

    final searchResults = _notifications.where((notification) {
      return notification.title.toLowerCase().contains(query.toLowerCase()) ||
          notification.message.toLowerCase().contains(query.toLowerCase());
    }).toList();

    _filteredNotifications = searchResults;
    notifyListeners();
  }

  // Get notification by ID
  NotificationModel? getNotificationById(int id) {
    try {
      return _notifications.firstWhere((n) => n.id == id);
    } catch (e) {
      return null;
    }
  }

  // Refresh notifications
  Future<void> refresh(String userType) async {
    await loadNotifications(userType, refresh: true);
  }

  // Download attachment
  Future<String?> downloadAttachment(int attachmentId, String userType) async {
    try {
      final filePath =
          await _apiService.downloadAttachment(attachmentId, userType);
      return filePath;
    } catch (e) {
      _setError(e.toString());
      return null;
    }
  }

  // Add new notification (for real-time updates)
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    _applyFilter();
  }

  // Update notification
  void updateNotification(NotificationModel notification) {
    final index = _notifications.indexWhere((n) => n.id == notification.id);
    if (index != -1) {
      _notifications[index] = notification;
      _applyFilter();
    }
  }

  // Remove notification
  void removeNotification(int notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    _applyFilter();
  }

  // Clear all notifications
  void clearNotifications() {
    _notifications.clear();
    _filteredNotifications.clear();
    _currentPage = 1;
    _hasMoreData = true;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Get filter display name
  String getFilterDisplayName(String filter) {
    switch (filter) {
      case AppConfig.notificationTypeAll:
        return 'All';
      case AppConfig.notificationTypeRead:
        return 'Read';
      case AppConfig.notificationTypeUnread:
        return 'Unread';
      default:
        return 'Unknown';
    }
  }

  // Check if notification is expanded (for UI state)
  final Set<int> _expandedNotifications = <int>{};

  bool isExpanded(int notificationId) {
    return _expandedNotifications.contains(notificationId);
  }

  void toggleExpanded(int notificationId) {
    if (_expandedNotifications.contains(notificationId)) {
      _expandedNotifications.remove(notificationId);
    } else {
      _expandedNotifications.add(notificationId);
    }
    notifyListeners();
  }

  void collapseAll() {
    _expandedNotifications.clear();
    notifyListeners();
  }
}
