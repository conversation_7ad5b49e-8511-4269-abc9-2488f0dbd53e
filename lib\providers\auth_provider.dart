import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../config/app_config.dart';

class AuthProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  UserModel? _user;
  String? _userType;
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get user => _user;
  String? get userType => _userType;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool get isStudent => _userType == AppConfig.userTypeStudent;
  bool get isEmployee => _userType == AppConfig.userTypeEmployee;
  bool get isAdmin => _userType == AppConfig.userTypeAdmin;

  // Initialize auth state from storage
  Future<void> initializeAuth() async {
    _setLoading(true);
    
    try {
      final isLoggedIn = await _storageService.isLoggedIn();
      final userType = await _storageService.getUserType();
      final userData = await _storageService.getUserData();

      if (isLoggedIn && userType != null && userData != null) {
        _user = UserModel.fromJson(userData, userType);
        _userType = userType;
        _isLoggedIn = true;
      }
    } catch (e) {
      _setError('Failed to initialize authentication: ${e.toString()}');
      await _storageService.clearSession();
    }
    
    _setLoading(false);
  }

  // Login method
  Future<bool> login(String identifier, String password, String userType) async {
    _setLoading(true);
    _clearError();

    try {
      // Use real API login
      final user = await _apiService.login(identifier, password, userType);
      
      _user = user;
      _userType = userType;
      _isLoggedIn = true;
      
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      if (_userType != null) {
        await _apiService.logout(_userType!);
      }
    } catch (e) {
      // Continue with logout even if API call fails
      debugPrint('Logout API error: ${e.toString()}');
    }
    
    // Clear local data
    _user = null;
    _userType = null;
    _isLoggedIn = false;
    
    _setLoading(false);
    notifyListeners();
  }

  // Update user data
  void updateUser(UserModel user) {
    _user = user;
    _storageService.saveUserData(user.toJson());
    notifyListeners();
  }

  // Check if token is still valid
  Future<bool> validateToken() async {
    if (!_isLoggedIn || _userType == null) return false;
    
    try {
      // Try to make a simple API call to validate token
      final isValid = await _apiService.checkConnection();
      if (!isValid) {
        await logout();
        return false;
      }
      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Get user display name
  String get userDisplayName {
    if (_user == null) return '';
    return _user!.displayName;
  }

  // Get user identifier (phone/email)
  String get userIdentifier {
    if (_user == null) return '';
    return _user!.identifier;
  }

  // Get user info for display
  String get userInfo {
    if (_user == null) return '';
    return _user!.displayInfo;
  }

  // Check if user has specific permission
  bool hasPermission(String permission) {
    switch (permission) {
      case 'send_notifications':
        return isAdmin;
      case 'manage_users':
        return isAdmin;
      case 'view_notifications':
        return isLoggedIn;
      case 'download_attachments':
        return isStudent || isEmployee;
      default:
        return false;
    }
  }

}
