name: notysend_app
description: NotiSend - Notification Management System
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Design
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Storage & Persistence
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # Authentication & Security
  crypto: ^3.0.3
  
  # File Handling
  file_picker: ^6.1.1
  open_file: ^3.3.2
  path_provider: ^2.1.1
  permission_handler: ^11.0.1
  
  # Push Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  
  # Utilities
  intl: ^0.18.1
  url_launcher: ^6.2.1
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  
  # UI Components
  flutter_staggered_animations: ^1.1.1
  pull_to_refresh: ^2.0.0
  flutter_spinkit: ^5.2.0
  fluttertoast: ^8.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
