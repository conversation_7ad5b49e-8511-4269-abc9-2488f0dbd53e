import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../providers/auth_provider.dart';
import '../../routes/app_routes.dart';
import '../../config/app_theme.dart';
import '../../config/app_config.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _identifierController = TextEditingController();
  final _passwordController = TextEditingController();

  String _selectedUserType = AppConfig.userTypeStudent;
  bool _obscurePassword = true;
  bool _rememberMe = false;

  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Animation initialization removed as animations are not used

    _animationController.forward();
  }

  @override
  void dispose() {
    _identifierController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  String get _identifierLabel {
    switch (_selectedUserType) {
      case AppConfig.userTypeStudent:
        return 'رقم الهاتف';
      case AppConfig.userTypeEmployee:
        return 'رقم الجوال';
      case AppConfig.userTypeAdmin:
        return 'البريد الإلكتروني';
      default:
        return 'المعرف';
    }
  }

  String get _identifierHint {
    switch (_selectedUserType) {
      case AppConfig.userTypeStudent:
        return 'أدخل رقم هاتفك';
      case AppConfig.userTypeEmployee:
        return 'أدخل رقم جوالك';
      case AppConfig.userTypeAdmin:
        return 'أدخل بريدك الإلكتروني';
      default:
        return 'أدخل المعرف';
    }
  }

  IconData get _identifierIcon {
    switch (_selectedUserType) {
      case AppConfig.userTypeStudent:
      case AppConfig.userTypeEmployee:
        return Icons.phone;
      case AppConfig.userTypeAdmin:
        return Icons.email;
      default:
        return Icons.person;
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.login(
      _identifierController.text.trim(),
      _passwordController.text,
      _selectedUserType,
    );

    if (!mounted) return;

    if (success) {
      AppRoutes.navigateToDashboard(context, _selectedUserType);
    } else {
      _showErrorSnackBar(authProvider.error ?? 'فشل تسجيل الدخول');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.dangerColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: AnimationLimiter(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 600),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 50.0,
                  child: FadeInAnimation(child: widget),
                ),
                children: [
                  const SizedBox(height: 40),

                  // Logo and Title
                  _buildHeader(),

                  const SizedBox(height: 48),

                  // User Type Selector
                  _buildUserTypeSelector(),

                  const SizedBox(height: 32),

                  // Login Form
                  _buildLoginForm(),

                  const SizedBox(height: 32),

                  // Login Button
                  _buildLoginButton(),

                  const SizedBox(height: 24),

                  // Additional Options
                  _buildAdditionalOptions(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.notifications_active,
            size: 40,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 24),

        // Welcome Text
        Text(
          'مرحباً بك في ${AppConfig.appName}',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        Text(
          'سجل دخولك للوصول إلى إشعاراتك',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserTypeSelector() {
    return Container(
      decoration: AppTheme.cardDecoration,
      padding: const EdgeInsets.all(4),
      child: Row(
        children: [
          _buildUserTypeTab(AppConfig.userTypeStudent, 'طالب', Icons.school),
          _buildUserTypeTab(AppConfig.userTypeEmployee, 'موظف', Icons.work),
          _buildUserTypeTab(
              AppConfig.userTypeAdmin, 'مدير', Icons.admin_panel_settings),
        ],
      ),
    );
  }

  Widget _buildUserTypeTab(String userType, String label, IconData icon) {
    final isSelected = _selectedUserType == userType;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedUserType = userType;
            _identifierController.clear();
            _passwordController.clear();
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: AppTheme.labelMedium.copyWith(
                  color: isSelected ? Colors.white : AppTheme.textSecondary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Identifier Field
          CustomTextField(
            controller: _identifierController,
            labelText: _identifierLabel,
            hintText: _identifierHint,
            prefixIcon: _identifierIcon,
            keyboardType: _selectedUserType == AppConfig.userTypeAdmin
                ? TextInputType.emailAddress
                : TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال $_identifierLabel';
              }

              if (_selectedUserType == AppConfig.userTypeAdmin) {
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
              }

              return null;
            },
          ),

          const SizedBox(height: 16),

          // Password Field
          CustomTextField(
            controller: _passwordController,
            labelText: 'كلمة المرور',
            hintText: 'أدخل كلمة المرور',
            prefixIcon: Icons.lock,
            obscureText: _obscurePassword,
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
                color: AppTheme.textSecondary,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال كلمة المرور';
              }
              if (value.length < AppConfig.minPasswordLength) {
                return 'كلمة المرور يجب أن تكون ${AppConfig.minPasswordLength} أحرف على الأقل';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return CustomButton(
          text: 'تسجيل الدخول',
          onPressed: authProvider.isLoading ? null : _handleLogin,
          isLoading: authProvider.isLoading,
          icon: Icons.login,
          width: double.infinity,
        );
      },
    );
  }

  Widget _buildAdditionalOptions() {
    return Column(
      children: [
        // Remember Me
        Row(
          children: [
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value ?? false;
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
            Text(
              'تذكرني',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // App Info
        Text(
          'الإصدار ${AppConfig.appVersion}',
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textMuted,
          ),
        ),
      ],
    );
  }
}
