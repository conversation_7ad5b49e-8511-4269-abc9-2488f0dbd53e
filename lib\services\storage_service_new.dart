import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  bool _initializationFailed = false;

  // Initialize SharedPreferences
  Future<void> init() async {
    if (_isInitialized || _initializationFailed) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('Storage service initialized successfully');
    } catch (e) {
      debugPrint('SharedPreferences initialization failed: $e');
      _initializationFailed = true;
      // Don't rethrow - continue without storage
    }
  }

  // Safe method to get preferences
  Future<SharedPreferences?> get _safePrefs async {
    if (_initializationFailed) return null;
    
    if (!_isInitialized) {
      await init();
    }
    
    return _prefs;
  }

  // Token Management
  Future<void> saveToken(String token) async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.setString(AppConfig.userTokenKey, token);
    }
  }

  Future<String?> getToken() async {
    final preferences = await _safePrefs;
    return preferences?.getString(AppConfig.userTokenKey);
  }

  Future<void> removeToken() async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.remove(AppConfig.userTokenKey);
    }
  }

  // User Type Management
  Future<void> saveUserType(String userType) async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.setString(AppConfig.userTypeKey, userType);
    }
  }

  Future<String?> getUserType() async {
    final preferences = await _safePrefs;
    return preferences?.getString(AppConfig.userTypeKey);
  }

  Future<void> removeUserType() async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.remove(AppConfig.userTypeKey);
    }
  }

  // User Data Management
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      final userDataJson = jsonEncode(userData);
      await preferences.setString(AppConfig.userDataKey, userDataJson);
    }
  }

  Future<Map<String, dynamic>?> getUserData() async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      final userDataJson = preferences.getString(AppConfig.userDataKey);
      if (userDataJson != null) {
        return jsonDecode(userDataJson) as Map<String, dynamic>;
      }
    }
    return null;
  }

  Future<void> removeUserData() async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.remove(AppConfig.userDataKey);
    }
  }

  // Login Status Management
  Future<void> setLoggedIn(bool isLoggedIn) async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.setBool(AppConfig.isLoggedInKey, isLoggedIn);
    }
  }

  Future<bool> isLoggedIn() async {
    final preferences = await _safePrefs;
    return preferences?.getBool(AppConfig.isLoggedInKey) ?? false;
  }

  Future<void> removeLoggedInStatus() async {
    final preferences = await _safePrefs;
    if (preferences != null) {
      await preferences.remove(AppConfig.isLoggedInKey);
    }
  }

  // Clear user session data only
  Future<void> clearSession() async {
    await removeToken();
    await removeUserType();
    await removeUserData();
    await removeLoggedInStatus();
  }

  // Check if storage is healthy
  Future<bool> isHealthy() async {
    try {
      final preferences = await _safePrefs;
      if (preferences != null) {
        await preferences.setString('health_check', 'test');
        final value = preferences.getString('health_check');
        await preferences.remove('health_check');
        return value == 'test';
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
