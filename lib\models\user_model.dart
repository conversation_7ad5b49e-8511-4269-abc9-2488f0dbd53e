class UserModel {
  final int id;
  final String name;
  final String email;
  final String? phoneNumber;
  final String? mobile;
  final String userType;
  final String? className;
  final String? nationality;
  final String? parentPhoneNumber;
  final String? contractType;
  final String? employeeType;
  final String? jobStatus;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.mobile,
    required this.userType,
    this.className,
    this.nationality,
    this.parentPhoneNumber,
    this.contractType,
    this.employeeType,
    this.jobStatus,
    this.createdAt,
    this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json, String userType) {
    switch (userType) {
      case 'student':
        return UserModel(
          id: json['id'] ?? 0,
          name: json['full_name'] ?? '',
          email: '', // Students don't have email in your system
          phoneNumber: json['phone_number'] ?? '',
          userType: userType,
          className: json['class'] ?? '',
          nationality: json['nationality'] ?? '',
          parentPhoneNumber: json['parent_phone_number'] ?? '',
          createdAt: json['created_at'] != null 
              ? DateTime.parse(json['created_at']) 
              : null,
          updatedAt: json['updated_at'] != null 
              ? DateTime.parse(json['updated_at']) 
              : null,
        );
      
      case 'employee':
        return UserModel(
          id: json['id'] ?? 0,
          name: json['full_name'] ?? '',
          email: '', // Employees don't have email in your system
          mobile: json['mobile'] ?? '',
          userType: userType,
          contractType: json['contract_type'] ?? '',
          employeeType: json['employee_type'] ?? '',
          jobStatus: json['job_status'] ?? '',
          createdAt: json['created_at'] != null 
              ? DateTime.parse(json['created_at']) 
              : null,
          updatedAt: json['updated_at'] != null 
              ? DateTime.parse(json['updated_at']) 
              : null,
        );
      
      case 'admin':
        return UserModel(
          id: json['id'] ?? 0,
          name: json['name'] ?? '',
          email: json['email'] ?? '',
          userType: userType,
          createdAt: json['created_at'] != null 
              ? DateTime.parse(json['created_at']) 
              : null,
          updatedAt: json['updated_at'] != null 
              ? DateTime.parse(json['updated_at']) 
              : null,
        );
      
      default:
        throw Exception('Unknown user type: $userType');
    }
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'id': id,
      'user_type': userType,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };

    switch (userType) {
      case 'student':
        json.addAll({
          'full_name': name,
          'phone_number': phoneNumber,
          'class': className,
          'nationality': nationality,
          'parent_phone_number': parentPhoneNumber,
        });
        break;
      
      case 'employee':
        json.addAll({
          'full_name': name,
          'mobile': mobile,
          'contract_type': contractType,
          'employee_type': employeeType,
          'job_status': jobStatus,
        });
        break;
      
      case 'admin':
        json.addAll({
          'name': name,
          'email': email,
        });
        break;
    }

    return json;
  }

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? mobile,
    String? userType,
    String? className,
    String? nationality,
    String? parentPhoneNumber,
    String? contractType,
    String? employeeType,
    String? jobStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      mobile: mobile ?? this.mobile,
      userType: userType ?? this.userType,
      className: className ?? this.className,
      nationality: nationality ?? this.nationality,
      parentPhoneNumber: parentPhoneNumber ?? this.parentPhoneNumber,
      contractType: contractType ?? this.contractType,
      employeeType: employeeType ?? this.employeeType,
      jobStatus: jobStatus ?? this.jobStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => name;
  
  String get identifier {
    switch (userType) {
      case 'student':
        return phoneNumber ?? '';
      case 'employee':
        return mobile ?? '';
      case 'admin':
        return email;
      default:
        return '';
    }
  }

  String get displayInfo {
    switch (userType) {
      case 'student':
        return className ?? '';
      case 'employee':
        return contractType ?? '';
      case 'admin':
        return 'Administrator';
      default:
        return '';
    }
  }

  bool get isStudent => userType == 'student';
  bool get isEmployee => userType == 'employee';
  bool get isAdmin => userType == 'admin';

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, userType: $userType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.userType == userType;
  }

  @override
  int get hashCode => id.hashCode ^ userType.hashCode;
}
