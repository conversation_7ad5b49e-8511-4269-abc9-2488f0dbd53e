class AppConfig {
  // API Configuration
  static const String baseUrl = 'http://notysend.test/api'; // Replace with your actual domain
  static const String localUrl = 'http://127.0.0.1:8000/api'; // Laravel local development
  
  // Use local URL for development, base URL for production
  static const String apiUrl = localUrl;
  
  // API Endpoints (matching your Laravel routes)
  static const String loginEndpoint = '/login';
  static const String logoutEndpoint = '/logout';
  
  // Student Endpoints
  static const String studentLoginEndpoint = '/student/login';
  static const String studentDashboardEndpoint = '/student/dashboard';
  static const String studentNotificationsEndpoint = '/student/notifications';
  static const String studentMarkAsReadEndpoint = '/student/notifications/{id}/read';
  static const String studentAttachmentsDownloadEndpoint = '/student/attachments/{id}/download';
  static const String studentAttachmentsViewEndpoint = '/student/attachments/{id}/view';
  
  // Employee Endpoints
  static const String employeeLoginEndpoint = '/employee/login';
  static const String employeeDashboardEndpoint = '/employee/dashboard';
  static const String employeeNotificationsEndpoint = '/employee/notifications';
  static const String employeeMarkAsReadEndpoint = '/employee/notifications/{id}/read';
  static const String employeeAttachmentsDownloadEndpoint = '/employee/attachments/{id}/download';
  static const String employeeAttachmentsViewEndpoint = '/employee/attachments/{id}/view';
  
  // Admin Endpoints
  static const String adminLoginEndpoint = '/admin/login';
  static const String adminDashboardEndpoint = '/admin/dashboard';
  static const String adminNotificationsEndpoint = '/admin/notifications';
  static const String adminSendNotificationEndpoint = '/admin/notifications';
  static const String adminStudentsEndpoint = '/admin/students';
  static const String adminEmployeesEndpoint = '/admin/emp';
  static const String adminUsersEndpoint = '/admin/admin-users';
  
  // App Configuration
  static const String appName = 'NotiSend';
  static const String appVersion = '1.0.0';
  static const int timeoutDuration = 30; // seconds
  static const int maxRetries = 3;
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userTypeKey = 'user_type';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String lastSyncKey = 'last_sync';
  static const String notificationSettingsKey = 'notification_settings';
  
  // User Types
  static const String userTypeStudent = 'student';
  static const String userTypeEmployee = 'employee';
  static const String userTypeAdmin = 'admin';
  
  // Notification Types
  static const String notificationTypeAll = 'all';
  static const String notificationTypeRead = 'read';
  static const String notificationTypeUnread = 'unread';
  
  // File Types
  static const List<String> supportedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  static const List<String> supportedDocumentTypes = ['pdf', 'doc', 'docx', 'txt'];
  static const int maxFileSize = 25 * 1024 * 1024; // 25MB
  
  // Colors (matching your web app)
  static const String primaryColor = '#6b8e23';
  static const String primaryDark = '#556b2f';
  static const String primaryLight = '#8fbc8f';
  static const String secondaryColor = '#f0f8e8';
  static const String successColor = '#228b22';
  static const String warningColor = '#daa520';
  static const String dangerColor = '#dc2626';
  static const String textPrimary = '#2d4a22';
  static const String textSecondary = '#556b2f';
  static const String textMuted = '#8fbc8f';
  static const String backgroundColor = '#e3e3e3';
  static const String cardBackground = '#ffffff';
  static const String messageTextColor = '#47732B';
  
  // Animation Durations
  static const int shortAnimationDuration = 200;
  static const int mediumAnimationDuration = 300;
  static const int longAnimationDuration = 500;
  
  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;
  
  // Cache Settings
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100; // MB
  
  // Push Notification Settings
  static const String fcmTopicAll = 'all_users';
  static const String fcmTopicStudents = 'students';
  static const String fcmTopicEmployees = 'employees';
  static const String fcmTopicAdmins = 'admins';
  
  // Error Messages
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error. Please try again later.';
  static const String authErrorMessage = 'Authentication failed. Please login again.';
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  
  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String logoutSuccessMessage = 'Logout successful!';
  static const String markAsReadSuccessMessage = 'Notification marked as read!';
  static const String downloadSuccessMessage = 'File downloaded successfully!';
  
  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxNameLength = 255;
  static const int maxEmailLength = 255;
  
  // Helper Methods
  static String getFullUrl(String endpoint) {
    return '$apiUrl$endpoint';
  }
  
  static String replaceUrlParams(String endpoint, Map<String, dynamic> params) {
    String result = endpoint;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });
    return result;
  }
  
  static bool isImageFile(String extension) {
    return supportedImageTypes.contains(extension.toLowerCase());
  }
  
  static bool isDocumentFile(String extension) {
    return supportedDocumentTypes.contains(extension.toLowerCase());
  }
  
  static String getFileIcon(String extension) {
    if (isImageFile(extension)) return 'image';
    if (extension.toLowerCase() == 'pdf') return 'pdf';
    if (['doc', 'docx'].contains(extension.toLowerCase())) return 'word';
    return 'file';
  }
}
