import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../models/user_model.dart';
import '../models/notification_model.dart';
import 'storage_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final StorageService _storage = StorageService();
  
  // HTTP Client with timeout
  http.Client get _client => http.Client();

  // Headers for API requests
  Future<Map<String, String>> get _headers async {
    final token = await _storage.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Generic API request method
  Future<Map<String, dynamic>> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    try {
      final uri = Uri.parse(AppConfig.getFullUrl(endpoint));
      final uriWithQuery = queryParams != null 
          ? uri.replace(queryParameters: queryParams)
          : uri;

      final headers = await _headers;
      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(uriWithQuery, headers: headers)
              .timeout(Duration(seconds: AppConfig.timeoutDuration));
          break;
        case 'POST':
          response = await _client.post(
            uriWithQuery,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(seconds: AppConfig.timeoutDuration));
          break;
        case 'PUT':
          response = await _client.put(
            uriWithQuery,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(seconds: AppConfig.timeoutDuration));
          break;
        case 'DELETE':
          response = await _client.delete(uriWithQuery, headers: headers)
              .timeout(Duration(seconds: AppConfig.timeoutDuration));
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      return _handleResponse(response);
    } on SocketException {
      throw Exception(AppConfig.networkErrorMessage);
    } on HttpException {
      throw Exception(AppConfig.serverErrorMessage);
    } catch (e) {
      throw Exception('Request failed: ${e.toString()}');
    }
  }

  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    try {
      final data = jsonDecode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return data;
      } else if (response.statusCode == 401) {
        final message = data['message'] ?? AppConfig.authErrorMessage;
        throw Exception(message);
      } else if (response.statusCode == 422) {
        // Laravel validation errors
        final errors = data['errors'] ?? {};
        final firstError = errors.values.isNotEmpty ? errors.values.first[0] : 'Validation failed';
        throw Exception(firstError);
      } else if (response.statusCode >= 500) {
        throw Exception(AppConfig.serverErrorMessage);
      } else {
        final message = data['message'] ?? AppConfig.genericErrorMessage;
        throw Exception(message);
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Failed to parse server response');
    }
  }

  // Authentication Methods
  Future<UserModel> login(String identifier, String password, String userType) async {
    String endpoint;
    Map<String, dynamic> body;

    switch (userType) {
      case AppConfig.userTypeStudent:
        endpoint = AppConfig.studentLoginEndpoint;
        body = {'phone_number': identifier, 'password': password};
        break;
      case AppConfig.userTypeEmployee:
        endpoint = AppConfig.employeeLoginEndpoint;
        body = {'mobile': identifier, 'password': password};
        break;
      case AppConfig.userTypeAdmin:
        endpoint = AppConfig.adminLoginEndpoint;
        body = {'email': identifier, 'password': password};
        break;
      default:
        throw Exception('Invalid user type');
    }

    final response = await _makeRequest('POST', endpoint, body: body);
    
    // Store token and user data
    String? token;
    if (response['access_token'] != null) {
      token = response['access_token'];
    } else if (response['token'] != null) {
      token = response['token'];
    }
    
    if (token != null) {
      await _storage.saveToken(token);
      await _storage.saveUserType(userType);
      await _storage.setLoggedIn(true);
    }

    // Handle different response structures
    Map<String, dynamic> userData;
    if (response['user'] != null) {
      userData = response['user'];
    } else if (response['data'] != null) {
      userData = response['data'];
    } else {
      userData = response;
    }
    
    final user = UserModel.fromJson(userData, userType);
    await _storage.saveUserData(user.toJson());

    return user;
  }

  Future<void> logout(String userType) async {
    String endpoint;
    
    switch (userType) {
      case AppConfig.userTypeStudent:
        endpoint = '/student/logout';
        break;
      case AppConfig.userTypeEmployee:
        endpoint = '/employee/logout';
        break;
      case AppConfig.userTypeAdmin:
        endpoint = '/admin/logout';
        break;
      default:
        throw Exception('Invalid user type');
    }

    try {
      await _makeRequest('POST', endpoint);
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      await _storage.clearAll();
    }
  }

  // Notification Methods
  Future<List<NotificationModel>> getNotifications(String userType, {
    String filter = 'all',
    int page = 1,
    int limit = AppConfig.defaultPageSize,
  }) async {
    String endpoint;
    
    switch (userType) {
      case AppConfig.userTypeStudent:
        endpoint = AppConfig.studentDashboardEndpoint;
        break;
      case AppConfig.userTypeEmployee:
        endpoint = AppConfig.employeeDashboardEndpoint;
        break;
      case AppConfig.userTypeAdmin:
        endpoint = AppConfig.adminNotificationsEndpoint;
        break;
      default:
        throw Exception('Invalid user type');
    }

    final queryParams = {
      'filter': filter,
      'page': page.toString(),
      'limit': limit.toString(),
    };

    final response = await _makeRequest('GET', endpoint, queryParams: queryParams);
    
    // Handle different response structures from Laravel
    List<dynamic> notificationsData;
    if (response['notifications'] != null) {
      notificationsData = response['notifications'];
    } else if (response['data'] != null) {
      notificationsData = response['data'];
    } else if (response is List) {
      notificationsData = response;
    } else {
      notificationsData = [];
    }
    
    return notificationsData
        .map((notification) => NotificationModel.fromJson(notification))
        .toList();
  }

  Future<void> markNotificationAsRead(int notificationId, String userType) async {
    String endpoint;
    
    switch (userType) {
      case AppConfig.userTypeStudent:
        endpoint = AppConfig.replaceUrlParams(
          AppConfig.studentMarkAsReadEndpoint,
          {'id': notificationId},
        );
        break;
      case AppConfig.userTypeEmployee:
        endpoint = AppConfig.replaceUrlParams(
          AppConfig.employeeMarkAsReadEndpoint,
          {'id': notificationId},
        );
        break;
      default:
        throw Exception('Invalid user type for marking as read');
    }

    await _makeRequest('POST', endpoint);
  }

  // File Download Methods
  Future<String> downloadAttachment(int attachmentId, String userType) async {
    String endpoint;
    
    switch (userType) {
      case AppConfig.userTypeStudent:
        endpoint = AppConfig.replaceUrlParams(
          AppConfig.studentAttachmentsDownloadEndpoint,
          {'id': attachmentId},
        );
        break;
      case AppConfig.userTypeEmployee:
        endpoint = AppConfig.replaceUrlParams(
          AppConfig.employeeAttachmentsDownloadEndpoint,
          {'id': attachmentId},
        );
        break;
      default:
        throw Exception('Invalid user type for download');
    }

    final uri = Uri.parse(AppConfig.getFullUrl(endpoint));
    final headers = await _headers;
    
    final response = await _client.get(uri, headers: headers)
        .timeout(Duration(seconds: AppConfig.timeoutDuration * 2)); // Longer timeout for downloads

    if (response.statusCode == 200) {
      // Return the file path where it should be saved
      // This will be handled by the download service
      return response.body;
    } else {
      throw Exception('Failed to download file');
    }
  }

  // Admin specific methods
  Future<Map<String, dynamic>> getDashboardStats(String userType) async {
    if (userType != AppConfig.userTypeAdmin) {
      throw Exception('Only admins can access dashboard stats');
    }

    final response = await _makeRequest('GET', AppConfig.adminDashboardEndpoint);
    return response;
  }

  Future<void> sendNotification(Map<String, dynamic> notificationData) async {
    await _makeRequest('POST', AppConfig.adminSendNotificationEndpoint, body: notificationData);
  }

  // Health check
  Future<bool> checkConnection() async {
    try {
      final response = await _client.get(
        Uri.parse('${AppConfig.apiUrl}/health'),
        headers: {'Accept': 'application/json'},
      ).timeout(Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // Dispose method
  void dispose() {
    _client.close();
  }
}
